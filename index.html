<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="./logo-white.avif" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Rise & Hustle</title>
  
  <!-- Preload Critical Fonts -->
  <link rel="preload" href="/src/assets/font/Haydes.ttf" as="font" type="font/ttf" crossorigin>
  <link rel="preload" href="/src/assets/font/google-fonts/Poppins-Regular.woff2" as="font" type="font/woff2" crossorigin>

  <!-- Preload Critical Images for LCP -->
  <link rel="preload" href="/src/assets/images/logo-white.avif" as="image" type="image/avif">
  <link rel="preload" href="/src/assets/images/background.avif" as="image" type="image/avif">
  <link rel="preload" href="/src/assets/banner/dealer_level_up.avif" as="image" type="image/avif">
  <link rel="preload" href="/src/assets/game_logo/crypto.avif" as="image" type="image/avif">
  <link rel="preload" href="/src/assets/game_logo/lootbox.avif" as="image" type="image/avif">

  <!-- Resource Hints for Performance -->
  <link rel="dns-prefetch" href="//images.unsplash.com">
  <link rel="dns-prefetch" href="//static.zdassets.com">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Performance Meta Tags -->
  <meta name="format-detection" content="telephone=no">
  <meta name="theme-color" content="#1a1b1e">
  <meta name="color-scheme" content="dark">

  <!-- Critical CSS Inline -->
  <style>
    /* Critical above-the-fold styles */
    :root { background-color: #1a1b1e; color: #ffffff; }
    body { margin: 0; font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif; background-color: #1a1b1e; color: #ffffff; }
    .min-h-screen { min-height: 100vh; }
    .bg-black { background-color: #000000; }
    .text-white { color: #ffffff; }
    /* Loading spinner for critical path */
    .loading-spinner { display: flex; align-items: center; justify-content: center; min-height: 100vh; background-color: #000000; }
    .spinner { width: 3rem; height: 3rem; border: 2px solid transparent; border-bottom: 2px solid #ED0CFF; border-radius: 50%; animation: spin 1s linear infinite; }
    @keyframes spin { to { transform: rotate(360deg); } }
  </style>

</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
  <!-- Start of playzuzu Zendesk Widget script -->
  <!-- <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=92959ea6-5ea6-48ce-9079-015c2410b765"></script> -->
  <!-- End of playzuzu Zendesk Widget script -->
</body>

</html>