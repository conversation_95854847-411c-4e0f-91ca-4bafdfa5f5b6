#!/usr/bin/env node

/**
 * Bundle Analysis Script for Rise & Hustle
 * Analyzes bundle size and suggests optimizations
 */

const fs = require('fs');
const path = require('path');

const DIST_DIR = path.join(__dirname, '../dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundle() {
  console.log('🔍 Analyzing bundle size...\n');
  
  if (!fs.existsSync(DIST_DIR)) {
    console.log('❌ Build directory not found. Run "npm run build" first.');
    return;
  }
  
  const files = fs.readdirSync(ASSETS_DIR);
  const jsFiles = files.filter(file => file.endsWith('.js'));
  const cssFiles = files.filter(file => file.endsWith('.css'));
  const assetFiles = files.filter(file => !file.endsWith('.js') && !file.endsWith('.css'));
  
  let totalSize = 0;
  let jsSize = 0;
  let cssSize = 0;
  let assetSize = 0;
  
  console.log('📦 JavaScript Files:');
  jsFiles.forEach(file => {
    const filePath = path.join(ASSETS_DIR, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalSize += size;
    jsSize += size;
    
    const status = size > 500 * 1024 ? '⚠️ ' : size > 200 * 1024 ? '⚡' : '✅';
    console.log(`  ${status} ${file}: ${formatBytes(size)}`);
  });
  
  console.log('\n🎨 CSS Files:');
  cssFiles.forEach(file => {
    const filePath = path.join(ASSETS_DIR, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalSize += size;
    cssSize += size;
    
    const status = size > 100 * 1024 ? '⚠️ ' : '✅';
    console.log(`  ${status} ${file}: ${formatBytes(size)}`);
  });
  
  console.log('\n🖼️  Asset Files:');
  assetFiles.slice(0, 10).forEach(file => {
    const filePath = path.join(ASSETS_DIR, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalSize += size;
    assetSize += size;
    
    const status = size > 200 * 1024 ? '⚠️ ' : '✅';
    console.log(`  ${status} ${file}: ${formatBytes(size)}`);
  });
  
  if (assetFiles.length > 10) {
    console.log(`  ... and ${assetFiles.length - 10} more files`);
  }
  
  console.log('\n📊 Bundle Summary:');
  console.log(`  JavaScript: ${formatBytes(jsSize)} (${((jsSize/totalSize)*100).toFixed(1)}%)`);
  console.log(`  CSS: ${formatBytes(cssSize)} (${((cssSize/totalSize)*100).toFixed(1)}%)`);
  console.log(`  Assets: ${formatBytes(assetSize)} (${((assetSize/totalSize)*100).toFixed(1)}%)`);
  console.log(`  Total: ${formatBytes(totalSize)}`);
  
  console.log('\n💡 Optimization Recommendations:');
  
  if (jsSize > 1024 * 1024) { // > 1MB
    console.log('  ⚠️  JavaScript bundle is large (>1MB)');
    console.log('     - Consider code splitting');
    console.log('     - Remove unused dependencies');
    console.log('     - Use dynamic imports for non-critical code');
  }
  
  if (cssSize > 200 * 1024) { // > 200KB
    console.log('  ⚠️  CSS bundle is large (>200KB)');
    console.log('     - Remove unused CSS');
    console.log('     - Consider CSS code splitting');
  }
  
  if (assetSize > 5 * 1024 * 1024) { // > 5MB
    console.log('  ⚠️  Assets are large (>5MB)');
    console.log('     - Optimize images');
    console.log('     - Use modern image formats (AVIF, WebP)');
    console.log('     - Consider lazy loading');
  }
  
  // Performance targets
  console.log('\n🎯 Performance Targets:');
  console.log('  ✅ Initial JS bundle should be < 200KB');
  console.log('  ✅ CSS should be < 50KB');
  console.log('  ✅ Critical images should be < 100KB each');
  
  const initialJSSize = jsFiles
    .filter(file => file.includes('index') || !file.includes('-'))
    .reduce((total, file) => {
      const filePath = path.join(ASSETS_DIR, file);
      return total + fs.statSync(filePath).size;
    }, 0);
  
  if (initialJSSize < 200 * 1024) {
    console.log(`  ✅ Initial JS bundle: ${formatBytes(initialJSSize)} (Good!)`);
  } else {
    console.log(`  ⚠️  Initial JS bundle: ${formatBytes(initialJSSize)} (Too large!)`);
  }
}

function suggestOptimizations() {
  console.log('\n🚀 Suggested Optimizations:');
  console.log('1. Enable gzip/brotli compression on server');
  console.log('2. Use CDN for static assets');
  console.log('3. Implement service worker caching');
  console.log('4. Optimize images with modern formats');
  console.log('5. Remove unused dependencies');
  console.log('6. Use tree shaking for libraries');
  console.log('7. Implement route-based code splitting');
}

function checkDependencies() {
  console.log('\n📋 Checking package.json for optimization opportunities...');
  
  const packagePath = path.join(__dirname, '../package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  const dependencies = packageJson.dependencies || {};
  const devDependencies = packageJson.devDependencies || {};
  
  console.log(`  Dependencies: ${Object.keys(dependencies).length}`);
  console.log(`  Dev Dependencies: ${Object.keys(devDependencies).length}`);
  
  // Check for potentially large dependencies
  const largeDeps = [
    'lodash', 'moment', 'jquery', 'bootstrap', 'material-ui',
    'antd', 'three', 'chart.js', 'recharts'
  ];
  
  const foundLargeDeps = Object.keys(dependencies).filter(dep => 
    largeDeps.some(large => dep.includes(large))
  );
  
  if (foundLargeDeps.length > 0) {
    console.log('  ⚠️  Large dependencies found:');
    foundLargeDeps.forEach(dep => {
      console.log(`     - ${dep} (consider alternatives or tree shaking)`);
    });
  } else {
    console.log('  ✅ No obviously large dependencies found');
  }
}

// Main execution
console.log('🚀 Rise & Hustle - Bundle Analysis\n');
console.log('='.repeat(50));

analyzeBundle();
checkDependencies();
suggestOptimizations();

console.log('\n✅ Analysis complete!');
console.log('\n📋 Next Steps:');
console.log('1. Review large files and optimize them');
console.log('2. Remove unused dependencies');
console.log('3. Implement suggested optimizations');
console.log('4. Re-run analysis after changes');
