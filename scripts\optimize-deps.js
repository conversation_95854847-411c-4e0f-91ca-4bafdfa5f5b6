#!/usr/bin/env node

/**
 * Dependency Optimization Script
 * Analyzes and suggests dependency optimizations
 */

const fs = require('fs');
const path = require('path');

const packagePath = path.join(__dirname, '../package.json');
const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

function analyzeDependencies() {
  console.log('🔍 Analyzing dependencies for optimization...\n');
  
  const dependencies = packageJson.dependencies || {};
  const devDependencies = packageJson.devDependencies || {};
  
  console.log('📦 Current Dependencies:');
  Object.entries(dependencies).forEach(([name, version]) => {
    console.log(`  ${name}: ${version}`);
  });
  
  console.log('\n🛠️  Dev Dependencies:');
  Object.entries(devDependencies).forEach(([name, version]) => {
    console.log(`  ${name}: ${version}`);
  });
  
  // Check for duplicate functionality
  console.log('\n⚠️  Potential Issues:');
  
  // Toast libraries
  if (dependencies['react-hot-toast'] && dependencies['react-toastify']) {
    console.log('  🔄 Duplicate toast libraries found:');
    console.log('     - react-hot-toast');
    console.log('     - react-toastify');
    console.log('     Recommendation: Choose one and remove the other');
  }
  
  // Webpack plugin in Vite project
  if (dependencies['workbox-webpack-plugin']) {
    console.log('  ❌ workbox-webpack-plugin found in Vite project');
    console.log('     Recommendation: Remove and use Vite PWA plugin instead');
  }
  
  // Large libraries
  const largeDeps = {
    'konva': 'Consider if full Konva is needed or use lighter alternatives',
    'react-konva': 'Heavy canvas library - ensure it\'s necessary',
    'embla-carousel-react': 'Check if simpler carousel solutions exist',
    'react-data-table-component': 'Consider lighter table alternatives'
  };
  
  Object.entries(largeDeps).forEach(([dep, suggestion]) => {
    if (dependencies[dep]) {
      console.log(`  📊 ${dep}: ${suggestion}`);
    }
  });
  
  // Bundle size estimates
  console.log('\n📏 Estimated Bundle Impact:');
  const bundleSizes = {
    'react': '42KB',
    'react-dom': '130KB',
    'react-router-dom': '25KB',
    'axios': '15KB',
    '@tanstack/react-query': '35KB',
    'konva': '300KB+',
    'react-konva': '50KB',
    'formik': '45KB',
    'yup': '60KB',
    'lucide-react': '200KB+ (if not tree-shaken)',
    'socket.io-client': '200KB+',
    'react-data-table-component': '100KB+',
    'embla-carousel-react': '80KB+'
  };
  
  let totalEstimated = 0;
  Object.entries(dependencies).forEach(([dep, version]) => {
    if (bundleSizes[dep]) {
      console.log(`  ${dep}: ~${bundleSizes[dep]}`);
      // Extract number for total calculation
      const sizeMatch = bundleSizes[dep].match(/(\d+)/);
      if (sizeMatch) {
        totalEstimated += parseInt(sizeMatch[1]);
      }
    }
  });
  
  console.log(`\n  Estimated total: ~${totalEstimated}KB+ (before compression)`);
}

function suggestOptimizations() {
  console.log('\n🚀 Optimization Suggestions:');
  
  console.log('\n1. Remove Duplicate Dependencies:');
  console.log('   npm uninstall react-toastify  # Keep react-hot-toast');
  console.log('   npm uninstall workbox-webpack-plugin  # Not needed in Vite');
  
  console.log('\n2. Tree Shaking Optimizations:');
  console.log('   - Import only needed icons from lucide-react');
  console.log('   - Use specific imports: import { Icon } from "lucide-react"');
  
  console.log('\n3. Consider Lighter Alternatives:');
  console.log('   - Replace heavy libraries with lighter alternatives');
  console.log('   - Use native browser APIs where possible');
  
  console.log('\n4. Dynamic Imports:');
  console.log('   - Move heavy libraries to dynamic imports');
  console.log('   - Load game-specific libraries only when needed');
  
  console.log('\n5. Bundle Analysis:');
  console.log('   npm run build:analyze  # Analyze actual bundle sizes');
}

function generateOptimizedPackageJson() {
  console.log('\n📝 Generating optimized package.json suggestions...');
  
  const optimizedDeps = { ...packageJson.dependencies };
  
  // Remove duplicates
  delete optimizedDeps['react-toastify'];
  delete optimizedDeps['workbox-webpack-plugin'];
  
  console.log('\n✅ Suggested package.json dependencies:');
  console.log(JSON.stringify({ dependencies: optimizedDeps }, null, 2));
  
  const savings = Object.keys(packageJson.dependencies).length - Object.keys(optimizedDeps).length;
  console.log(`\n💰 Potential savings: ${savings} dependencies removed`);
}

function checkUnusedDependencies() {
  console.log('\n🔍 Checking for potentially unused dependencies...');
  console.log('(Manual verification required)');
  
  const potentiallyUnused = [
    'react-zendesk',
    'use-image',
    'dayjs'
  ];
  
  potentiallyUnused.forEach(dep => {
    if (packageJson.dependencies[dep]) {
      console.log(`  ❓ ${dep} - Verify if still needed`);
    }
  });
}

// Main execution
console.log('🚀 Rise & Hustle - Dependency Optimization\n');
console.log('='.repeat(50));

analyzeDependencies();
suggestOptimizations();
generateOptimizedPackageJson();
checkUnusedDependencies();

console.log('\n✅ Analysis complete!');
console.log('\n📋 Next Steps:');
console.log('1. Remove duplicate dependencies');
console.log('2. Implement tree shaking for large libraries');
console.log('3. Consider lighter alternatives');
console.log('4. Run bundle analysis to verify improvements');
