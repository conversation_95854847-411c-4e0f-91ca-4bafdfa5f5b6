#!/usr/bin/env node

/**
 * Image Optimization Script
 * This script helps optimize images for better LCP performance
 */

const fs = require('fs');
const path = require('path');

const ASSETS_DIR = path.join(__dirname, '../src/assets');
const CRITICAL_IMAGES = [
  'images/logo-white.avif',
  'images/background.avif',
  'banner/dealer_level_up.avif',
  'game_logo/crypto.avif',
  'game_logo/lootbox.avif',
  'game_logo/plinko.avif',
  'game_logo/dice.avif'
];

function analyzeImageSizes() {
  console.log('🔍 Analyzing critical image sizes...\n');
  
  CRITICAL_IMAGES.forEach(imagePath => {
    const fullPath = path.join(ASSETS_DIR, imagePath);
    
    if (fs.existsSync(fullPath)) {
      const stats = fs.statSync(fullPath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      
      console.log(`📸 ${imagePath}: ${sizeKB} KB`);
      
      if (stats.size > 100 * 1024) { // > 100KB
        console.log(`   ⚠️  Large image detected! Consider optimization.`);
      }
    } else {
      console.log(`❌ ${imagePath}: File not found`);
    }
  });
  
  console.log('\n💡 Optimization Tips:');
  console.log('- Images > 100KB should be optimized');
  console.log('- Use AVIF format for better compression');
  console.log('- Consider responsive images for different screen sizes');
  console.log('- Preload critical above-the-fold images');
}

function generatePreloadLinks() {
  console.log('\n🔗 Generated preload links for HTML:');
  console.log('<!-- Add these to your index.html head section -->');
  
  CRITICAL_IMAGES.forEach(imagePath => {
    const fullPath = path.join(ASSETS_DIR, imagePath);
    if (fs.existsSync(fullPath)) {
      console.log(`<link rel="preload" href="/src/assets/${imagePath}" as="image" type="image/avif">`);
    }
  });
}

function checkImageFormats() {
  console.log('\n🎨 Checking image format distribution...\n');
  
  const formats = {};
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scanDirectory(itemPath);
      } else {
        const ext = path.extname(item).toLowerCase();
        if (['.jpg', '.jpeg', '.png', '.webp', '.avif'].includes(ext)) {
          formats[ext] = (formats[ext] || 0) + 1;
        }
      }
    });
  }
  
  scanDirectory(ASSETS_DIR);
  
  Object.entries(formats).forEach(([format, count]) => {
    console.log(`${format}: ${count} files`);
  });
  
  if (formats['.png'] > 0 || formats['.jpg'] > 0 || formats['.jpeg'] > 0) {
    console.log('\n⚠️  Consider converting legacy formats to AVIF/WebP for better compression');
  }
}

// Main execution
console.log('🚀 Rise & Hustle - Image Optimization Analysis\n');
console.log('='.repeat(50));

analyzeImageSizes();
generatePreloadLinks();
checkImageFormats();

console.log('\n✅ Analysis complete!');
console.log('\n📋 Next Steps:');
console.log('1. Optimize large images (>100KB)');
console.log('2. Add preload links to index.html');
console.log('3. Convert legacy formats to AVIF/WebP');
console.log('4. Test LCP improvements with Lighthouse');
