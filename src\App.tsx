import React, { useEffect, useState, Suspense } from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Static imports for critical components
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import { useAuth } from './auth/AuthContext';
import { GET_BALANCE_API } from './api/auth';
import { useSocketContext } from './context/socketProvider';
import { useGameDataLoading } from './hooks/useGameDataLoading';
import AdService from './api/addService';
import { preloadCriticalResources, prefetchResources } from './utils/preloader';
import { lazyWithPreload } from './utils/lazyWithPreload';

// Dynamic imports with lazyWithPreload
const Home = lazyWithPreload(() => import('./pages/Home'));
const Shop = lazyWithPreload(() => import('./pages/Shop'));
const Wallet = lazyWithPreload(() => import('./pages/Wallet'));
const Rewards = lazyWithPreload(() => import('./pages/Rewards'));
const Leaderboard = lazyWithPreload(() => import('./pages/Leaderboard'));
const Squads = lazyWithPreload(() => import('./pages/Squads'));
const Auth = lazyWithPreload(() => import('./pages/Auth'));
const Map = lazyWithPreload(() => import('./pages/Map'));
const Lottery = lazyWithPreload(() => import('./pages/Lottery'));

// Game components
const CrashGame = lazyWithPreload(() => import('./pages/CrashGame'));
const CrashGame2 = lazyWithPreload(() => import('./pages/CrashGame2'));
const CryptoKing = lazyWithPreload(() => import('./pages/CryptoKing'));
const HigherLowerGame = lazyWithPreload(() => import('./pages/HigherLowerGame'));
const RollDiceGame = lazyWithPreload(() => import('./pages/RollDiceGame'));
const PlinkoGame = lazyWithPreload(() => import('./pages/PlinkoGame'));

// Modal components
const StoryIntro = lazyWithPreload(() => import('./components/StoryIntro'));
const ScratchCard = lazyWithPreload(() => import('./components/ScratchCard'));
const BoostModal = lazyWithPreload(() => import('./components/BoostModal'));
const WheelOfFortune = lazyWithPreload(() => import('./components/WheelOfFortune'));
const TermsAndConditionsPage = lazyWithPreload(() => import('./components/TermsAndConditions'));
const PrivacyPolicyPage = lazyWithPreload(() => import('./components/PrivacyPolicy'));

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen bg-black">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
  </div>
);

function App() {
  const [balance, setBalance] = useState({
    coins: 0,
    fpp: 0,
    level: 0,
    xp: 0,
    real_money: '0',
  });
  const { connectSocket, isConnected } = useSocketContext();
  useGameDataLoading();

  const { userProfile, loadUserProfile, isAuthenticated, login, accessToken } =
    useAuth();
  const [showStoryIntro, setShowStoryIntro] = useState(false);
  const [showScratchCard, setShowScratchCard] = useState(false);
  const [showBoostModal, setShowBoostModal] = useState(false);
  const [showWheel, setShowWheel] = useState(false);
  const [data, setData] = useState<any[]>([]);

  const fetchBalance = () =>
    fetch(GET_BALANCE_API, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
      .then((res) => {
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        return res.json();
      })
      .then(
        (
          data: {
            bonus_money: string;
            currency: string;
            id: string;
            real_money: string;
            updated_at: string;
            user_id: string;
          }[]
        ) => {
          const coins = Number(
            data.find((balance) => balance.currency === 'P')?.real_money
          );
          setData(data);
          setBalance({ ...balance, coins });
        }
      )
      .catch((err) => console.error('Balance fetch failed:', err));

  useEffect(() => {
    if (!isAuthenticated || !accessToken) return;

    loadUserProfile();
    fetchBalance();
  }, [isAuthenticated, accessToken]);

  useEffect(() => {
    const hasSeenIntro = window.localStorage.getItem('hasSeenStoryIntro');

    if (!hasSeenIntro) {
      const timer = setTimeout(() => setShowStoryIntro(true), 100);
      return () => clearTimeout(timer);
    }
  }, []);

  useEffect(() => {
    if (!isConnected) {
      connectSocket();
    }
    
    // Preload critical resources
    preloadCriticalResources();
    
    // Prefetch non-critical resources
    prefetchResources();
  }, [isConnected]);

  const handleLogin = async (token: string) => {
    login(token);
  };

  // Listen for custom events
  React.useEffect(() => {
    const handleShowScratchCard = () => {
      setShowScratchCard(true);
    };

    const handleShowBoostModal = () => {
      setShowBoostModal(true);
    };

    const handleShowWheel = () => {
      setShowWheel(true);
    };

    window.addEventListener('show-scratch-card', handleShowScratchCard);
    window.addEventListener('show-boost-modal', handleShowBoostModal);
    window.addEventListener('show-wheel', handleShowWheel);

    return () => {
      window.removeEventListener('show-scratch-card', handleShowScratchCard);
      window.removeEventListener('show-boost-modal', handleShowBoostModal);
      window.removeEventListener('show-wheel', handleShowWheel);
      AdService.cleanup();
    };
  }, []);

  const handleWin = (reward: { type: string; amount?: number }) => {
    if (reward.type === 'coins') {
      fetchBalance();
    } else if (reward.type === 'fpp') {
      setBalance((prev) => ({
        ...prev,
        fpp: prev.fpp + Number(reward.amount),
      }));
    }
  };

  const onFakePurchase = (cost: number) =>
    setBalance({ ...balance, coins: balance.coins - cost });

  const onPurchase = () => fetchBalance();

  const handleShare = (platform: string) => {
    setShowBoostModal(false);
    setBalance((prev) => ({
      ...prev,
      xp: Math.min(100, prev.xp + 10),
    }));
  };

  // --- Handle rewarded ad events ---
  useEffect(() => {
    AdService.listen((action) => {
      if (action === 'rewardAdClaimed') {
        console.log('Rewarded Ad claimed');
      } else if (action === 'rewardAdClaimIneligible') {
        console.log('Ineligible for reward');
      } else if (
        action === 'rewardAdClaimFailed' ||
        action === 'rewardedAdFailed'
      ) {
        console.log('Reward ad error');
      } else if (action === 'rewardAdCompleted') {
        console.log('Rewarded Ad completed');
      }
    });
  }, []);



  return (
    <Router>
      <Routes>
        <Route
          path="/auth"
          element={
            isAuthenticated ? (
              <Navigate to="/" replace />
            ) : (
              <Suspense fallback={<LoadingSpinner />}>
                <Auth onLogin={handleLogin} />
              </Suspense>
            )
          }
        />

        <Route
          path="/"
          element={
            <>

              <Layout balance={balance} />

              {userProfile && showStoryIntro && (
                <div className="fixed inset-0 bg-black z-50">
                  <Suspense fallback={<LoadingSpinner />}>
                    <StoryIntro onClose={() => setShowStoryIntro(false)} />
                  </Suspense>
                </div>
              )}

              {showScratchCard && (
                <Suspense fallback={<LoadingSpinner />}>
                  <ScratchCard
                    onClose={() => setShowScratchCard(false)}
                    onWin={handleWin}
                    balance={balance.coins}
                    onPurchase={(cost) => {
                      setBalance((prev) => ({
                        ...prev,
                        coins: prev.coins - cost,
                      }));
                    }}
                  />
                </Suspense>
              )}
              {showBoostModal && (
                <Suspense fallback={<LoadingSpinner />}>
                  <BoostModal
                    onClose={() => setShowBoostModal(false)}
                    onWatchAd={() => {
                      setShowBoostModal(false);
                      if (userProfile) {
                        AdService.showRewardedAd({
                          userId: userProfile?.user_id,
                        });
                      }
                    }}
                    onShare={handleShare}
                  />
                </Suspense>
              )}
              {showWheel && (
                <Suspense fallback={<LoadingSpinner />}>
                  <WheelOfFortune
                    onClose={() => setShowWheel(false)}
                    onWin={handleWin}
                  />
                </Suspense>
              )}

              <ToastContainer position="top-right" />
            </>
          }
        >

          <Route
            path="/terms-and-conditions"
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <TermsAndConditionsPage />
              </Suspense>
            }
          />
          <Route 
            path="/privacy-policy" 
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <PrivacyPolicyPage />
              </Suspense>
            } 
          />
          <Route
            index
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <Home balance={balance} onBalanceChange={setBalance} />
              </Suspense>
            }
          />

          <Route
            path="shop"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <Shop balance={balance} onBalanceChange={setBalance} />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="wallet"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <Wallet balance={balance} />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="squads"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <Squads />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="rewards"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <Rewards balance={balance} onBalanceChange={setBalance} />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="leaderboard"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <Leaderboard />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="map"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <Map />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="lottery"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <Lottery />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="street-king"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <CrashGame
                    onWin={handleWin}
                    onPurchase={onPurchase}
                    balance={balance.coins}
                  />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="street-king-2"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <CrashGame2
                    onWin={handleWin}
                    onPurchase={onPurchase}
                    balance={balance.coins}
                  />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="crypto-king"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <CryptoKing
                    onPurchase={onFakePurchase}
                    onWin={handleWin}
                    balance={balance.coins}
                  />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="quick-hustle"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <HigherLowerGame
                    onWin={handleWin}
                    onPurchase={onPurchase}
                    balance={balance.coins}
                  />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="roll-dice"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <RollDiceGame
                    onPurchase={onFakePurchase}
                    onWin={handleWin}
                    balance={balance.coins}
                  />
                </Suspense>
              </ProtectedRoute>
            }
          />
          <Route
            path="plinko"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Suspense fallback={<LoadingSpinner />}>
                  <PlinkoGame
                    onPurchase={onFakePurchase}
                    onWin={handleWin}
                    balance={balance.coins}
                  />
                </Suspense>
              </ProtectedRoute>
            }
          />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;