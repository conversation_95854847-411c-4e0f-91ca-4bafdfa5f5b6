import React, { useState, useEffect, useRef } from 'react';

interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  priority?: boolean;
  sizes?: string;
  width?: number;
  height?: number;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  breakpoints?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  className = '',
  priority = false,
  sizes = '100vw',
  width,
  height,
  quality = 80,
  placeholder = 'empty',
  breakpoints = {
    mobile: 480,
    tablet: 768,
    desktop: 1200
  }
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [currentBreakpoint, setCurrentBreakpoint] = useState('desktop');
  const imgRef = useRef<HTMLImageElement>(null);

  // Detect screen size
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width <= breakpoints.mobile) {
        setCurrentBreakpoint('mobile');
      } else if (width <= breakpoints.tablet) {
        setCurrentBreakpoint('tablet');
      } else {
        setCurrentBreakpoint('desktop');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, [breakpoints]);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { rootMargin: '50px' }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView]);

  // Generate responsive image URLs
  const generateImageUrl = (baseUrl: string, width: number, format: string = 'avif') => {
    // For now, we'll use the original URL structure
    // In a real CDN setup, you'd modify this to use your CDN's URL structure
    const extension = baseUrl.split('.').pop();
    const baseName = baseUrl.replace(`.${extension}`, '');
    
    // If already in modern format, return as is
    if (baseUrl.includes('.avif') || baseUrl.includes('.webp')) {
      return baseUrl;
    }
    
    // Generate modern format URL
    return `${baseName}.${format}`;
  };

  // Generate srcSet for different screen sizes
  const generateSrcSet = (format: string) => {
    const baseUrl = src;
    const sizes = [
      { width: breakpoints.mobile, suffix: 'mobile' },
      { width: breakpoints.tablet, suffix: 'tablet' },
      { width: breakpoints.desktop, suffix: 'desktop' }
    ];

    return sizes
      .map(size => `${generateImageUrl(baseUrl, size.width, format)} ${size.width}w`)
      .join(', ');
  };

  // Get optimized image size based on current breakpoint
  const getOptimizedSize = () => {
    switch (currentBreakpoint) {
      case 'mobile':
        return { width: Math.min(width || 480, 480), height: height ? Math.round((height * 480) / (width || 480)) : undefined };
      case 'tablet':
        return { width: Math.min(width || 768, 768), height: height ? Math.round((height * 768) / (width || 768)) : undefined };
      default:
        return { width, height };
    }
  };

  const optimizedSize = getOptimizedSize();

  // Create CSS classes for styling
  const getImageClasses = () => {
    let classes = `${className} transition-all duration-300 ease-in-out`;
    
    if (!isLoaded && placeholder === 'blur') {
      classes += ' blur-sm bg-gray-800';
    }
    
    if (!isLoaded) {
      classes += ' opacity-0';
    } else {
      classes += ' opacity-100';
    }
    
    if (width && height) {
      classes += ` aspect-[${width}/${height}]`;
    }
    
    return classes;
  };

  // Blur placeholder for better UX
  const blurPlaceholder = 
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiMzMzMiIG9mZnNldD0iMjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iIzIyMiIgb2Zmc2V0PSI1MCUiLz48c3RvcCBzdG9wLWNvbG9yPSIjMzMzIiBvZmZzZXQ9IjcwJSIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZykiLz48L3N2Zz4=';

  return (
    <picture className="block">
      {isInView && (
        <>
          {/* AVIF format for modern browsers */}
          <source 
            srcSet={generateSrcSet('avif')} 
            type="image/avif" 
            sizes={sizes}
          />
          
          {/* WebP format for broader support */}
          <source 
            srcSet={generateSrcSet('webp')} 
            type="image/webp" 
            sizes={sizes}
          />
          
          {/* Fallback to original format */}
          <source 
            srcSet={generateSrcSet(src.split('.').pop() || 'jpg')} 
            type={`image/${src.split('.').pop()}`} 
            sizes={sizes}
          />
        </>
      )}
      
      <img
        ref={imgRef}
        src={isInView ? src : (placeholder === 'blur' ? blurPlaceholder : undefined)}
        alt={alt}
        className={getImageClasses()}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
        sizes={sizes}
        width={optimizedSize.width}
        height={optimizedSize.height}
        onLoad={() => setIsLoaded(true)}
        onError={() => setHasError(true)}
      />
      
      {hasError && (
        <div className={`${className} bg-gray-800 flex items-center justify-center text-gray-400 text-sm`}>
          <span>Failed to load image</span>
        </div>
      )}
    </picture>
  );
};

export default ResponsiveImage;
