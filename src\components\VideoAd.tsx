import React, { useState, useEffect, useRef } from 'react';
import { X, CheckCircle } from 'lucide-react';
import { postRewardClaim } from '../api/rewardsService';
import useFetch from '../hooks/useFetch';
import { GET_USER_PROFILE } from '../api/auth';
import { useAuth } from '../auth/AuthContext';
import { RewardGameType } from '../types/Rewards';

interface VideoAdProps {
  onClose: () => void;
  onComplete?: () => void;
}

const VideoAd: React.FC<VideoAdProps> = ({ onClose, onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [remainingTime, setRemainingTime] = useState(20);
  const { accessToken } = useAuth()
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [showReward, setShowReward] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const rewardTimeoutRef = useRef<NodeJS.Timeout>();

  const { data: userProfile } = useFetch(GET_USER_PROFILE, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  useEffect(() => {
    if (progress >= 100) {
      const handleReward = async () => {
        setShowReward(true);

        // ✅ Post reward claim API
        try {
          await postRewardClaim({
            user_id: userProfile?.user_id,
            game: RewardGameType.REWARDED_AD,
            position: 1,
          });
        } catch (error) {
          console.error('Reward claim failed:', error);
        }

        onComplete?.();

        rewardTimeoutRef.current = setTimeout(() => {
          onClose();
        }, 2000);
      };

      handleReward();
    }

    return () => {
      if (rewardTimeoutRef.current) {
        clearTimeout(rewardTimeoutRef.current);
      }
    };
  }, [progress, onComplete, onClose]);


  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime;
      const maxDuration = 20; // limit to 20 seconds
      const remaining = Math.max(maxDuration - current, 0);
      const percentage = ((current / maxDuration) * 100);

      setRemainingTime(Math.ceil(remaining));
      setProgress(Math.min(percentage, 100));

      if (current >= maxDuration) {
        videoRef.current.pause();
        setProgress(100);
      }
    }
  };



  const handleVideoLoad = () => {
    setIsVideoLoaded(true);
    if (videoRef.current) {

      videoRef.current.play().catch(error => {
        console.error('Video playback failed:', error);
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="   bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl p-6 w-full max-w-lg">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">Watch Ad to Earn Bucks</h2>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="relative">
          <div className="aspect-video w-full rounded-xl overflow-hidden bg-black">
            {!isVideoLoaded && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-pulse text-white/60">Loading video...</div>
              </div>
            )}
            <video
              ref={videoRef}

              autoPlay
              muted
              playsInline
              onLoadedData={handleVideoLoad}
              onTimeUpdate={handleTimeUpdate}
              className="w-full h-full object-cover"
              src="https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
            />
          </div>

          {/* Progress Bar */}
          <div className=" bottom-0 left-0 right-0 h-1 bg-white/20 mt-[20px]">
            <div
              className="h-full bg-yellow-400 transition-all duration-300 mt-[20px]"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Reward Notification */}
          {showReward && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm">
              <div className="text-center animate-fade-in">
                <CheckCircle size={48} className="text-green-400 mx-auto mb-3" />
                <p className="text-xl font-bold text-white">50 Bucks Earned! 🎉</p>
              </div>
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-between items-center">
          <p className="text-white/60 text-sm">
            Watch progress: {Math.round(progress)}%
          </p>
          <p className="text-sm font-medium text-yellow-400">
            ⏱ {remainingTime}s remaining
          </p>
          <p className="text-sm font-medium text-yellow-400">
            Reward: 50 Bucks
          </p>
        </div>
      </div>
    </div>
  );
};

export default VideoAd;