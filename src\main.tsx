// import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { AuthProvider } from './auth/AuthContext.tsx';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CoinsProvider } from './hooks/useCoinsQuery.tsx';
import { SocketProvider } from './context/socketProvider.tsx';
import { preloadCriticalResources } from './utils/preloader.ts';

const queryClient = new QueryClient();

// Preload critical resources immediately
preloadCriticalResources();
createRoot(document.getElementById('root')!).render(
  // <StrictMode>
    <AuthProvider>
      <SocketProvider>
    <QueryClientProvider client={queryClient}>
      <CoinsProvider>
    <App />
    </CoinsProvider>
    </QueryClientProvider>
    </SocketProvider>
    </AuthProvider>
  // </StrictMode>
);
