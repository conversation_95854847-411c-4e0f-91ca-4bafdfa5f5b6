// import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { AuthProvider } from './auth/AuthContext.tsx';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CoinsProvider } from './hooks/useCoinsQuery.tsx';
import { SocketProvider } from './context/socketProvider.tsx';
import { preloadCriticalResources } from './utils/preloader.ts';
import { startPerformanceMonitoring, measurePageLoad } from './utils/performanceMonitoring.ts';

const queryClient = new QueryClient();

// Start performance monitoring immediately
startPerformanceMonitoring();
measurePageLoad();

// Preload critical resources immediately
preloadCriticalResources();

// Register service worker for caching
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}
createRoot(document.getElementById('root')!).render(
  // <StrictMode>
    <AuthProvider>
      <SocketProvider>
    <QueryClientProvider client={queryClient}>
      <CoinsProvider>
    <App />
    </CoinsProvider>
    </QueryClientProvider>
    </SocketProvider>
    </AuthProvider>
  // </StrictMode>
);
