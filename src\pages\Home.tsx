import React, { Suspense, useState } from "react";
import { useAuth } from "../auth/AuthContext";
import LoginPromptPopup from "../components/LoginPromptPopup";
import { useNavigate } from "react-router-dom";
import { lazyWithPreload } from "../utils/lazyWithPreload";
import OptimizedImage from "../components/OptimizedImage";

import StoryCarousel from "../components/StoryCarousel";
const ScratchCard = lazyWithPreload(() => import("../components/ScratchCard"));
const WheelOfFortune = lazyWithPreload(() => import("../components/WheelOfFortune"));
const VideoAd = lazyWithPreload(() => import("../components/VideoAd"));
const BoostModal = lazyWithPreload(() => import("../components/BoostModal"));
import { IMAGES } from "../constant/image";


interface HomeProps {
  balance: {
    coins: number;
    fpp: number;
    level: number;
    xp: number;
  };
  onBalanceChange: (partial: Partial<HomeProps["balance"]>) => void;
}

const Home: React.FC<HomeProps> = ({ balance, onBalanceChange }) => {
  const { isAuthenticated } = useAuth();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const navigate = useNavigate();
  const [showScratchCard, setShowScratchCard] = useState(false);
  const [showWheel, setShowWheel] = useState(false);
  const [showVideoAd, setShowVideoAd] = useState(false);
  const [showBoostModal, setShowBoostModal] = useState(false);

  // If not authenticated, show login prompt on any card click
  const handleRequireLogin = (e?: React.MouseEvent) => {
    if (e) e.preventDefault();
    setShowLoginPrompt(true);
  };

  const handlePlayStreetHustle = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate("/street-king");
  };
  const handlePlayStreetHustle2 = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate("/street-king-2");
  };
  const handlePlayCryptoKing = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate("/crypto-king");
  };
  const handlePlayQuickHustle = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate("/quick-hustle");
  };
  const handlePlayRollDice = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate("/roll-dice");
  };
  const handlePlinkoGame = () => {
    if (!isAuthenticated) return handleRequireLogin();
    navigate("/plinko");
  };

  const handleShare = (platform: string) => {
    // Implement share functionality
    console.log(`Sharing to ${platform}`);
    setShowBoostModal(false);
  };

  const handleWin = (reward: { type: string; amount: number }) => {
    if (reward.type === "coins") {
      onBalanceChange({
        ...balance,
        coins: balance.coins + reward.amount,
      });
    } else if (reward.type === "fpp") {
      onBalanceChange({
        ...balance,
        fpp: balance.fpp + reward.amount,
      });
    }
  };

  return (
    <div className="pb-3">
      {/* Story Carousel */}
      <StoryCarousel />

      {/* Quick Games */}

      <div className="bg-black">
        <h3 className="text-[20px] leading-6 font-normal font-[Anton] text-white mb-5">
          Quick games
        </h3>

        <div className="grid grid-cols-2 gap-4 mt-8">
          {/* Scratch Card */}
          <div
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              setShowScratchCard(true);
            }}
            className={`
        relative
       rounded-[24px]
        p-4 sm:p-6
        bg-[#131313]
        shadow-md 
        transform transition-transform duration-300 
        hover:scale-[1.02]
        cursor-pointer
        flex flex-col justify-between
        min-h-[220px]
        z-10
        overflow-visible
      `}
          >
            <div
              className="
       absolute top-0 left-0
       w-full h-full
 rounded-[24px] bg-[radial-gradient(circle_at_bottom,_#ED0CFF_25%,_transparent_65%)]

      z-[12]
    bg-cover bg-center
  "
            />

            {/* Game Image - Updated positioning */}
            <div className="absolute top-[-60px] left-[px] w-[190px] z-10">
              <OptimizedImage
                src={IMAGES.SCRATCH_CARD}
                alt="Scratch Card"
                className="w-full h-auto object-contain drop-shadow-lg"
                sizes="190px"
                priority={true}
                width={190}
                height={190}
                placeholder="blur"
              />
            </div>
            {/* Content */}
            <div className="relative z-20 mt-24 flex-1 items-center">
              <h4 className="text-white text-center font-[Anton] text-lg sm:text-xl mb-1">
                Scratch Card
              </h4>
              <p className="text-gray-200 text-center text-xs sm:text-sm leading-snug">
                Instant wins up to ₦5,000
              </p>
            </div>

            {/* Cost Button */}
            <div className="relative z-20 mt-4 flex justify-center">
              <button className="bg-white text-black font-bold text-sm sm:text-base py-2 px-4 hover:text-[#ED0CFF] rounded-lg shadow hover:shadow-lg active:scale-95 transition-transform">
                +100 Bucks
              </button>
            </div>
          </div>

          {/* Wheel of Fortune */}
          <div
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              setShowWheel(true);
            }}
            className={`
        relative
        rounded-[24px]
        p-4 sm:p-6
        bg-[#131313]
        shadow-md 
        transform transition-transform duration-300
        hover:scale-[1.02]
        cursor-pointer
        flex flex-col justify-between
        overflow-visible
      `}
          >
            <div
              className="
             absolute top-0 left-0
    w-full h-full
  rounded-[24px] bg-[radial-gradient(circle_at_bottom,_#ED0CFF_25%,_transparent_65%)]
    z-[12]
    bg-cover bg-center
  "
            />
            {/* Game Image - Updated positioning */}
            <div className="absolute top-[-70px] right-[-10px] w-[230px] z-10">
              <OptimizedImage
                src={IMAGES.SPINNING_WHEEL}
                alt="Wheel of Fortune"
                className="w-full h-auto object-cover drop-shadow-lg"
                sizes="230px"
                priority={true}
                width={230}
                height={230}
                placeholder="blur"
              />
            </div>

            {/* Content */}
            <div className="relative z-20 mt-24 flex-1 ">
              <h4 className="text-white text-center font-[Anton] text-lg sm:text-xl mb-1">
                Wheel of Fortune
              </h4>
              <p className="text-gray-200 text-center text-xs sm:text-sm leading-snug">
                Spin to win big prizes
              </p>
            </div>

            {/* Cost Button */}
            <div className="relative z-20 mt-4 flex justify-center">
              <button className="bg-white text-black font-bold hover:text-[#ED0CFF] text-sm sm:text-base py-2 px-4 rounded-lg shadow hover:shadow-lg active:scale-95 transition-transform">
                +50 Bucks
              </button>
            </div>
          </div>
        </div>
      </div>

        <h3 className="text-[20px] leading-6 font-normal font-[Anton] text-white mt-8">
          Featured Games
        </h3>
      <div className="space-y-4 mt-3">
        <div className="grid grid-cols-2 gap-4 mt-14">
          {/* Crypto King */}
          <div
            onClick={handlePlayCryptoKing}
            className="relative
       rounded-[24px]
        p-4 sm:p-6
        bg-[#131313]
        shadow-md 
        transform transition-transform duration-300 
        hover:scale-[1.02]
        cursor-pointer
        flex flex-col justify-between
        min-h-[220px]
        z-10
        overflow-visible
  "
          >
            <div
              className="absolute top-0 left-0
       w-full h-full
 rounded-[24px] bg-[radial-gradient(circle_at_bottom,_#ED0CFF_20%,_transparent_55%)]



      z-[12]
    bg-cover bg-center
  "
            />

            {/* Floating image */}
            <div className="absolute  right-0 left-0 top-0 w-full z-10 bottom-0">
              <OptimizedImage
                src={IMAGES.CRYPTO_KING}
                alt="Crypto King"
                className="w-full h-auto object-contain drop-shadow-2xl mt-[-43px]"
                sizes="(max-width: 768px) 50vw, 25vw"
                priority={true}
                width={200}
                height={200}
                placeholder="blur"
              />
            </div>

            <div className="relative flex flex-col mt-3 items-center justify-end h-full w-full z-30 mt-auto">
              <h4 className="text-lg font-semibold font-[Anton] text-white text-center pb-4">
                Crypto King
              </h4>
            </div>
          </div>

          {/* Lootbox */}
          <div
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              // Add lootbox functionality here
              console.log("Lootbox clicked");
            }}
            className="relative
       rounded-[24px]
        p-4 sm:p-6
        bg-[#131313]
        shadow-md
        transform transition-transform duration-300
        hover:scale-[1.02]
        cursor-pointer
        flex flex-col justify-between
        min-h-[220px]
        z-10
        overflow-visible
  "
          >
            <div
              className="
             absolute top-0 left-0
       w-full h-full
 rounded-[24px] bg-[radial-gradient(circle_at_bottom,_#ED0CFF_18%,_transparent_55%)]

      z-[12]
    bg-cover bg-center
  "
            />

            {/* Floating image */}
            <div className="absolute  right-0 left-0 top-0 w-full z-10 bottom-0">
              <OptimizedImage
                src={IMAGES.LOOTBOX}
                alt="Lootbox"
                className="w-full h-auto object-contain drop-shadow-2xl mt-[-43px]"
                sizes="(max-width: 768px) 50vw, 25vw"
                priority={true}
                width={200}
                height={200}
                placeholder="blur"
              />
            </div>

            <div className="relative flex flex-col mt-3 items-center justify-end h-full w-full z-30 mt-auto">
              <h4 className="text-lg font-semibold font-[Anton] text-white text-center pb-4">
                Lootbox
              </h4>
            </div>
          </div>
        </div>
      </div>

      {/* Boost Section */}
      <div className=" rounded-xl">
        <div className="flex items-center justify-between mb-3 mt-3">
          <div>
            <h3 className="text-[20px] leading-6 font-normal font-[Anton] mb-1">
              Boost Your Progress
            </h3>
            <p className="text-white/60 font-poppins text-sm leading-5 font-normal">
              Watch ads or share to earn Bucks
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mt-10">
          <button
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              setShowVideoAd(true);
            }}
            className="
               relative 
    bg-gradient-to-b from-[#4f1154]  via-[#4f1154]  to-[#4f1154] 
    rounded-2xl pt-16 pb-6 px-6 
    flex flex-col items-center justify-center 
    transform transition-all duration-300 hover:scale-[1.02]
    shadow-lg
  "
          >
            <div
              className="
    absolute top-0 left-0
    w-full h-full
    bg-[linear-gradient(189deg,#000000c7,transparent)]
    z-[12]
    bg-cover bg-center
  "
            />

            {/* floated, rotated icon */}
            <div
              className="
              
    absolute 
    -top-8
    w-36 h-36 
    flex items-center justify-center
    z-30
  "
            >
              <img
                src={IMAGES.WATCH_ICON}
                alt="Watch Icon"
                loading="lazy"
                className="
        w-[217px] h-[217px]
        object-cover 
        rounded-xl 
        transform rotate-[-1deg]
        drop-shadow-2xl
      "
              />
            </div>

            {/* push content down to make room for the icon */}
            <p className="mt-10 font-normal font-[Anton] z-30">Watch Video</p>
            <p className="mt-2 text-gray-300 text-base z-30">+50 Bucks</p>
          </button>

          <button
            onClick={() => {
              if (!isAuthenticated) return handleRequireLogin();
              setShowBoostModal(true);
            }}
            className="
            relative 
   bg-gradient-to-b from-[#4f1154]  via-[#4f1154]  to-[#4f1154] 
  rounded-2xl pt-16 pb-6 px-6 
  flex flex-col items-center justify-center 
  transform transition-all duration-300 hover:scale-[1.02]
  shadow-lg
            "
          >
            <div
              className="
    absolute top-0 left-0
    w-full h-full
    bg-[linear-gradient(189deg,#000000c7,transparent)]
    z-[12]
    bg-cover bg-center
  "
            />
            {/* floated, rotated icon */}
            <div
              className="
              absolute 
              -top-8
              w-38 h-36 
              rounded-full 
              bg-transparent 
              flex items-center justify-center
              z-30
            "
            >
              <img
                src={IMAGES.MOREWAYS_ICON}
                alt={"More Way Icon"}
                loading="lazy"
                className="
                  w-[217px] h-[240px] 
                  object-cover 
                  rounded-xl 
                  transform 
                   rotate-[-1deg]

                  drop-shadow-2xl
                "
              />
            </div>

            {/* push content down to make room for the icon */}
            <h3 className="mt-10 font-normal font-[Anton] z-30">
              {"More Ways"}
            </h3>
            <p className="mt-2 text-gray-300 text-base z-30">
              {"Up to +100 Bucks"}
            </p>
          </button>
        </div>
      </div>

      {/* More Games */}
      <div className="space-y-4 mt-3">
        <h3 className="text-[20px] leading-6 font-normal font-[Anton]">
          More ways to chop
        </h3>

        {/* ─── Roll da Dice Card ─── */}
        <div
          onClick={handlePlayRollDice}
          className="relative rounded-xl overflow-hidden bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-white/10  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src={IMAGES.BIT_COINS}
                alt="Crypto King"
                loading="lazy"
                className="w-full h-full object-cover  rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-[−0.04em] font-[Anton]">
                Dice
              </h4>
              <p className="text-[12px] text-white/80">
                Test your luck, roll to win
              </p>
            </div>
          </div>
        </div>

        {/* ─── Plinko Card ─── */}
        <div
          onClick={handlePlinkoGame}
          className="relative rounded-xl overflow-hidden  bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-white/10  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src={IMAGES.PLINKO}
                alt="Crypto King"
                loading="lazy"
                className="w-full h-full object-cover  rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-[−0.04em] font-[Anton]">
                Plinko
              </h4>
              <p className="text-[12px] text-white/80">
                Drop chips and watch them bounce to big rewards
              </p>
            </div>
          </div>
        </div>

        {/* ─── Quick Hustle Card ─── */}
        <div
          onClick={handlePlayQuickHustle}
          className="relative rounded-xl overflow-hidden bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-white/10  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src={IMAGES.QUICK_HUSTULE}
                alt="Crypto King"
                loading="lazy"
                className="w-full h-full object-cover  rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-wide font-[Anton]">
              Hi/LO
              </h4>
              <p className="text-[12px] text-white/80">Fast games, instant wins</p>
            </div>
          </div>
        </div>

        <div
          onClick={handlePlayStreetHustle}
          className="relative rounded-xl overflow-hidden bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-white/10  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src="https://cdn.midjourney.com/415a32f8-f82e-4a92-b60c-267302e23f6b/0_2.png"
                alt="Crypto King"
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-[−0.04em] font-[Anton]">
                Street King
              </h4>
              <p className="text-[12px] text-white/80">Fast games, instant wins</p>
            </div>
          </div>
        </div>

        <div
          onClick={handlePlayStreetHustle2}
          className="relative rounded-xl overflow-hidden bg-[#131313] cursor-pointer shadow-sm transform transition-all duration-300 hover:scale-[1.02]"
        >
          <div className="absolute inset-0 bg-black/40 border border-[#ED0CFF]  rounded-xl"></div>
          <div className="relative z-10 flex items-center gap-4 p-4">
            <div className="bg-white/10  rounded-lg flex items-center justify-center w-16 h-16">
              <img
                src="https://cdn.midjourney.com/415a32f8-f82e-4a92-b60c-267302e23f6b/0_3.png"
                alt="Crypto King"
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[14px] font-normal leading-5 tracking-[−0.04em] font-[Anton]">
                Street King 2
              </h4>
              <p className="text-[12px] text-white/80">Fast games, instant wins</p>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {showScratchCard && (
  <Suspense fallback={<div>Loading...</div>}>
    <ScratchCard
      onClose={() => setShowScratchCard(false)}
      onWin={handleWin}
      balance={balance.coins}
      onPurchase={(cost) => {
        onBalanceChange({
          ...balance,
          coins: balance.coins - cost,
        });
      }}
    />
  </Suspense>
)}


      {showWheel && (
        <WheelOfFortune onClose={() => setShowWheel(false)} onWin={handleWin} />
      )}

      {showVideoAd && (
  <Suspense fallback={<div>Loading...</div>}>
    <VideoAd
      onClose={() => setShowVideoAd(false)}
      onComplete={() => {
        setShowVideoAd(false);
      }}
    />
  </Suspense>
)}


      {showBoostModal && (
  <Suspense fallback={<div>Loading...</div>}>
    <BoostModal
      onClose={() => setShowBoostModal(false)}
      onWatchAd={() => {
        setShowBoostModal(false);
        setShowVideoAd(true);
      }}
      onShare={handleShare}
    />
  </Suspense>
)}


      {/* Login Prompt Popup for unauthenticated actions */}
      <LoginPromptPopup isOpen={showLoginPrompt} onClose={() => setShowLoginPrompt(false)} />
    </div>
  );
};

export default Home;