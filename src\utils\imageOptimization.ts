/**
 * Image Optimization Utilities
 * Provides functions for optimizing image loading and performance
 */

// Image format support detection
export const supportsWebP = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

export const supportsAVIF = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const avif = new Image();
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2);
    };
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
};

// Get the best supported image format
export const getBestImageFormat = async (): Promise<'avif' | 'webp' | 'original'> => {
  if (await supportsAVIF()) return 'avif';
  if (await supportsWebP()) return 'webp';
  return 'original';
};

// Generate optimized image URL based on device capabilities
export const getOptimizedImageUrl = (
  originalUrl: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'avif' | 'webp' | 'original';
  } = {}
): string => {
  const { width, height, quality = 80, format } = options;
  
  // For now, return the original URL with format conversion
  // In a real CDN setup, you'd construct the optimized URL here
  if (format && format !== 'original') {
    const extension = originalUrl.split('.').pop();
    const baseName = originalUrl.replace(`.${extension}`, '');
    return `${baseName}.${format}`;
  }
  
  return originalUrl;
};

// Preload critical images with format detection
export const preloadOptimizedImage = async (url: string, options: {
  priority?: boolean;
  sizes?: string;
} = {}): Promise<void> => {
  const { priority = false, sizes = '100vw' } = options;
  const bestFormat = await getBestImageFormat();
  const optimizedUrl = getOptimizedImageUrl(url, { format: bestFormat });
  
  const link = document.createElement('link');
  link.rel = priority ? 'preload' : 'prefetch';
  link.as = 'image';
  link.href = optimizedUrl;
  link.sizes = sizes;
  
  if (bestFormat === 'avif') {
    link.type = 'image/avif';
  } else if (bestFormat === 'webp') {
    link.type = 'image/webp';
  }
  
  document.head.appendChild(link);
};

// Generate responsive image srcSet
export const generateSrcSet = (
  baseUrl: string,
  breakpoints: number[] = [480, 768, 1200, 1920]
): string => {
  return breakpoints
    .map(width => `${getOptimizedImageUrl(baseUrl, { width })} ${width}w`)
    .join(', ');
};

// Image loading performance tracker
export class ImagePerformanceTracker {
  private static instance: ImagePerformanceTracker;
  private loadTimes: Map<string, number> = new Map();
  private startTimes: Map<string, number> = new Map();

  static getInstance(): ImagePerformanceTracker {
    if (!ImagePerformanceTracker.instance) {
      ImagePerformanceTracker.instance = new ImagePerformanceTracker();
    }
    return ImagePerformanceTracker.instance;
  }

  startTracking(url: string): void {
    this.startTimes.set(url, performance.now());
  }

  endTracking(url: string): number {
    const startTime = this.startTimes.get(url);
    if (startTime) {
      const loadTime = performance.now() - startTime;
      this.loadTimes.set(url, loadTime);
      this.startTimes.delete(url);
      return loadTime;
    }
    return 0;
  }

  getAverageLoadTime(): number {
    const times = Array.from(this.loadTimes.values());
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }

  getSlowImages(threshold: number = 1000): string[] {
    return Array.from(this.loadTimes.entries())
      .filter(([_, time]) => time > threshold)
      .map(([url, _]) => url);
  }

  report(): void {
    console.log('📊 Image Performance Report:');
    console.log(`Average load time: ${this.getAverageLoadTime().toFixed(2)}ms`);
    console.log(`Total images tracked: ${this.loadTimes.size}`);
    
    const slowImages = this.getSlowImages();
    if (slowImages.length > 0) {
      console.log('⚠️ Slow loading images (>1s):');
      slowImages.forEach(url => {
        console.log(`  ${url}: ${this.loadTimes.get(url)?.toFixed(2)}ms`);
      });
    }
  }
}

// Lazy loading intersection observer utility
export const createLazyLoadObserver = (
  callback: (entry: IntersectionObserverEntry) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver => {
  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver((entries) => {
    entries.forEach(callback);
  }, defaultOptions);
};

// Image compression utility (client-side)
export const compressImage = (
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'jpeg' | 'webp';
  } = {}
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const { maxWidth = 1920, maxHeight = 1080, quality = 0.8, format = 'jpeg' } = options;
    
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        `image/${format}`,
        quality
      );
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

// Critical image preloader with performance tracking
export const preloadCriticalImages = async (urls: string[]): Promise<void> => {
  const tracker = ImagePerformanceTracker.getInstance();
  
  const promises = urls.map(async (url) => {
    tracker.startTracking(url);
    
    try {
      await preloadOptimizedImage(url, { priority: true });
      tracker.endTracking(url);
    } catch (error) {
      console.error(`Failed to preload image: ${url}`, error);
      tracker.endTracking(url);
    }
  });
  
  await Promise.all(promises);
  tracker.report();
};
