/**
 * Performance Monitoring and Core Web Vitals Tracking
 * Monitors LCP, FID, CLS, and other performance metrics
 */

interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

interface WebVitalsThresholds {
  LCP: { good: number; poor: number };
  FID: { good: number; poor: number };
  CLS: { good: number; poor: number };
  FCP: { good: number; poor: number };
  TTFB: { good: number; poor: number };
}

// Core Web Vitals thresholds (in milliseconds, except CLS)
const THRESHOLDS: WebVitalsThresholds = {
  LCP: { good: 2500, poor: 4000 },
  FID: { good: 100, poor: 300 },
  CLS: { good: 0.1, poor: 0.25 },
  FCP: { good: 1800, poor: 3000 },
  TTFB: { good: 800, poor: 1800 }
};

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetric> = new Map();
  private observers: Map<string, PerformanceObserver> = new Map();
  private isMonitoring = false;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private getRating(metricName: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const threshold = THRESHOLDS[metricName as keyof WebVitalsThresholds];
    if (!threshold) return 'good';
    
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  private recordMetric(name: string, value: number): void {
    const metric: PerformanceMetric = {
      name,
      value,
      rating: this.getRating(name, value),
      timestamp: Date.now()
    };

    this.metrics.set(name, metric);
    this.logMetric(metric);
    this.sendToAnalytics(metric);
  }

  private logMetric(metric: PerformanceMetric): void {
    const emoji = metric.rating === 'good' ? '✅' : metric.rating === 'needs-improvement' ? '⚡' : '❌';
    console.log(`${emoji} ${metric.name}: ${metric.value.toFixed(2)}ms (${metric.rating})`);
  }

  private sendToAnalytics(metric: PerformanceMetric): void {
    // Send to your analytics service
    // Example: Google Analytics 4, Mixpanel, etc.
    if (typeof gtag !== 'undefined') {
      gtag('event', 'web_vitals', {
        metric_name: metric.name,
        metric_value: metric.value,
        metric_rating: metric.rating
      });
    }
  }

  // Monitor Largest Contentful Paint (LCP)
  private monitorLCP(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1] as any;
      
      if (lastEntry) {
        this.recordMetric('LCP', lastEntry.startTime);
      }
    });

    observer.observe({ type: 'largest-contentful-paint', buffered: true });
    this.observers.set('LCP', observer);
  }

  // Monitor First Input Delay (FID)
  private monitorFID(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        this.recordMetric('FID', entry.processingStart - entry.startTime);
      });
    });

    observer.observe({ type: 'first-input', buffered: true });
    this.observers.set('FID', observer);
  }

  // Monitor Cumulative Layout Shift (CLS)
  private monitorCLS(): void {
    if (!('PerformanceObserver' in window)) return;

    let clsValue = 0;
    let sessionValue = 0;
    let sessionEntries: any[] = [];

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          const firstSessionEntry = sessionEntries[0];
          const lastSessionEntry = sessionEntries[sessionEntries.length - 1];

          if (sessionValue && 
              entry.startTime - lastSessionEntry.startTime < 1000 &&
              entry.startTime - firstSessionEntry.startTime < 5000) {
            sessionValue += entry.value;
            sessionEntries.push(entry);
          } else {
            sessionValue = entry.value;
            sessionEntries = [entry];
          }

          if (sessionValue > clsValue) {
            clsValue = sessionValue;
            this.recordMetric('CLS', clsValue);
          }
        }
      });
    });

    observer.observe({ type: 'layout-shift', buffered: true });
    this.observers.set('CLS', observer);
  }

  // Monitor First Contentful Paint (FCP)
  private monitorFCP(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (entry.name === 'first-contentful-paint') {
          this.recordMetric('FCP', entry.startTime);
        }
      });
    });

    observer.observe({ type: 'paint', buffered: true });
    this.observers.set('FCP', observer);
  }

  // Monitor Time to First Byte (TTFB)
  private monitorTTFB(): void {
    const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigationEntry) {
      const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
      this.recordMetric('TTFB', ttfb);
    }
  }

  // Monitor custom metrics
  public measureCustomMetric(name: string, startTime: number, endTime?: number): void {
    const value = endTime ? endTime - startTime : performance.now() - startTime;
    this.recordMetric(name, value);
  }

  // Start monitoring all metrics
  public startMonitoring(): void {
    if (this.isMonitoring) return;

    console.log('🚀 Starting performance monitoring...');
    
    this.monitorLCP();
    this.monitorFID();
    this.monitorCLS();
    this.monitorFCP();
    this.monitorTTFB();
    
    this.isMonitoring = true;

    // Monitor page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.generateReport();
      }
    });
  }

  // Stop monitoring
  public stopMonitoring(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.isMonitoring = false;
  }

  // Get current metrics
  public getMetrics(): Map<string, PerformanceMetric> {
    return new Map(this.metrics);
  }

  // Generate performance report
  public generateReport(): void {
    console.log('\n📊 Performance Report:');
    console.log('='.repeat(40));
    
    const coreMetrics = ['LCP', 'FID', 'CLS', 'FCP', 'TTFB'];
    
    coreMetrics.forEach(metricName => {
      const metric = this.metrics.get(metricName);
      if (metric) {
        const emoji = metric.rating === 'good' ? '✅' : metric.rating === 'needs-improvement' ? '⚡' : '❌';
        const unit = metricName === 'CLS' ? '' : 'ms';
        console.log(`${emoji} ${metricName}: ${metric.value.toFixed(2)}${unit} (${metric.rating})`);
      } else {
        console.log(`⏳ ${metricName}: Not measured yet`);
      }
    });

    // Overall score
    const measuredMetrics = coreMetrics.filter(name => this.metrics.has(name));
    const goodMetrics = measuredMetrics.filter(name => this.metrics.get(name)?.rating === 'good');
    const score = measuredMetrics.length > 0 ? (goodMetrics.length / measuredMetrics.length) * 100 : 0;
    
    console.log(`\n🎯 Overall Score: ${score.toFixed(0)}%`);
    
    if (score >= 80) {
      console.log('🎉 Excellent performance!');
    } else if (score >= 60) {
      console.log('👍 Good performance, room for improvement');
    } else {
      console.log('⚠️ Performance needs attention');
    }
  }

  // Get performance recommendations
  public getRecommendations(): string[] {
    const recommendations: string[] = [];
    
    const lcp = this.metrics.get('LCP');
    if (lcp && lcp.rating !== 'good') {
      recommendations.push('Optimize LCP: Preload critical images, reduce server response time');
    }

    const fid = this.metrics.get('FID');
    if (fid && fid.rating !== 'good') {
      recommendations.push('Optimize FID: Reduce JavaScript execution time, use code splitting');
    }

    const cls = this.metrics.get('CLS');
    if (cls && cls.rating !== 'good') {
      recommendations.push('Optimize CLS: Set image dimensions, avoid dynamic content insertion');
    }

    const fcp = this.metrics.get('FCP');
    if (fcp && fcp.rating !== 'good') {
      recommendations.push('Optimize FCP: Inline critical CSS, reduce render-blocking resources');
    }

    const ttfb = this.metrics.get('TTFB');
    if (ttfb && ttfb.rating !== 'good') {
      recommendations.push('Optimize TTFB: Improve server performance, use CDN');
    }

    return recommendations;
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Utility functions
export const startPerformanceMonitoring = (): void => {
  performanceMonitor.startMonitoring();
};

export const measurePageLoad = (): void => {
  window.addEventListener('load', () => {
    setTimeout(() => {
      performanceMonitor.generateReport();
      
      const recommendations = performanceMonitor.getRecommendations();
      if (recommendations.length > 0) {
        console.log('\n💡 Performance Recommendations:');
        recommendations.forEach((rec, index) => {
          console.log(`${index + 1}. ${rec}`);
        });
      }
    }, 1000);
  });
};

// Custom hook for React components
export const usePerformanceTracking = (componentName: string) => {
  const startTime = performance.now();
  
  return {
    markRenderComplete: () => {
      performanceMonitor.measureCustomMetric(`${componentName}_render`, startTime);
    },
    markInteractionComplete: (interactionName: string) => {
      performanceMonitor.measureCustomMetric(`${componentName}_${interactionName}`, startTime);
    }
  };
};
