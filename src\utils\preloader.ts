// Preload critical resources
export const preloadCriticalResources = () => {
  // Preload hero images and above-the-fold content
  const criticalImages = [
    '/src/assets/images/logo-white.avif',
    '/src/assets/images/background.avif',
    '/src/assets/banner/dealer_level_up.avif',
    '/src/assets/game_logo/crypto.avif',
    '/src/assets/game_logo/lootbox.avif',
    '/src/assets/game_logo/plinko.avif',
    '/src/assets/game_logo/dice.avif'
  ];

  criticalImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    link.type = 'image/avif';
    document.head.appendChild(link);
  });

  // Preload critical routes
  import('../pages/Home').then(() => {
    console.log('Home component preloaded');
  });
};

// Preload next likely images based on user interaction
export const preloadNextImages = (gameType?: string) => {
  const gameImages: Record<string, string[]> = {
    crypto: ['/src/assets/game_logo/crypto.avif'],
    lootbox: ['/src/assets/game_logo/lootbox.avif'],
    plinko: ['/src/assets/game_logo/plinko.avif'],
    dice: ['/src/assets/game_logo/dice.avif']
  };

  if (gameType && gameImages[gameType]) {
    gameImages[gameType].forEach(src => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });
  }
};

// Prefetch non-critical resources
export const prefetchResources = () => {
  const routes = [
    () => import('../pages/Wallet'),
    () => import('../pages/Shop'),
    () => import('../pages/Map')
  ];

  // Prefetch after initial load
  setTimeout(() => {
    routes.forEach(route => route());
  }, 2000);
};